import { Component, inject, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';

import { AuthService } from '../../../core/services/auth.service';
import { ThemeService } from '../../../core/services/theme.service';
import { ResponsiveService } from '../../../core/services/responsive.service';

interface NavigationItem {
  label: string;
  icon: string;
  route: string;
  badge?: number;
  tooltip?: string;
}

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatSidenavModule,
    MatListModule,
    MatDividerModule,
    MatBadgeModule,
    MatTooltipModule
  ],
  template: `
    <mat-sidenav-container class="sidenav-container">
      <!-- Side Navigation -->
      <mat-sidenav 
        #drawer 
        class="sidenav"
        [mode]="sidenavMode()"
        [opened]="sidenavOpened()"
        [fixedInViewport]="true"
        [fixedTopGap]="64"
        [fixedBottomGap]="0">
        
        <mat-nav-list>
          @for (item of navigationItems; track item.route) {
            <a mat-list-item 
               [routerLink]="item.route" 
               routerLinkActive="active-link"
               [matTooltip]="item.tooltip || item.label"
               [matTooltipDisabled]="!isMobile()"
               (click)="onNavItemClick()">
              <mat-icon matListItemIcon [matBadge]="item.badge" matBadgeColor="accent">
                {{ item.icon }}
              </mat-icon>
              <span matListItemTitle>{{ item.label }}</span>
            </a>
          }
        </mat-nav-list>

        <mat-divider></mat-divider>

        <!-- Theme Controls -->
        <div class="theme-controls" *ngIf="!isMobile()">
          <h3 class="theme-title">Theme</h3>
          <div class="theme-buttons">
            <button mat-icon-button 
                    [class.active]="themeMode() === 'light'"
                    (click)="setThemeMode('light')"
                    matTooltip="Light Mode">
              <mat-icon>light_mode</mat-icon>
            </button>
            <button mat-icon-button 
                    [class.active]="themeMode() === 'auto'"
                    (click)="setThemeMode('auto')"
                    matTooltip="Auto Mode">
              <mat-icon>brightness_auto</mat-icon>
            </button>
            <button mat-icon-button 
                    [class.active]="themeMode() === 'dark'"
                    (click)="setThemeMode('dark')"
                    matTooltip="Dark Mode">
              <mat-icon>dark_mode</mat-icon>
            </button>
          </div>
        </div>
      </mat-sidenav>

      <!-- Main Content -->
      <mat-sidenav-content>
        <!-- Top Toolbar -->
        <mat-toolbar color="primary" class="toolbar">
          <!-- Menu Button (Mobile) -->
          @if (isMobile()) {
            <button mat-icon-button (click)="toggleSidenav()" aria-label="Toggle menu">
              <mat-icon>menu</mat-icon>
            </button>
          }

          <!-- App Title -->
          <span class="app-title">
            <mat-icon class="app-icon">store</mat-icon>
            Bhavani Doors
          </span>

          <span class="spacer"></span>

          <!-- User Menu -->
          <button mat-icon-button [matMenuTriggerFor]="userMenu" aria-label="User menu">
            <mat-icon>account_circle</mat-icon>
          </button>
          
          <mat-menu #userMenu="matMenu">
            <div class="user-info">
              <div class="user-name">{{ user()?.first_name || user()?.username }}</div>
              <div class="user-email">{{ user()?.email }}</div>
            </div>
            <mat-divider></mat-divider>
            
            @if (isMobile()) {
              <button mat-menu-item (click)="setThemeMode('light')">
                <mat-icon>light_mode</mat-icon>
                <span>Light Mode</span>
              </button>
              <button mat-menu-item (click)="setThemeMode('dark')">
                <mat-icon>dark_mode</mat-icon>
                <span>Dark Mode</span>
              </button>
              <button mat-menu-item (click)="setThemeMode('auto')">
                <mat-icon>brightness_auto</mat-icon>
                <span>Auto Mode</span>
              </button>
              <mat-divider></mat-divider>
            }
            
            <button mat-menu-item (click)="logout()">
              <mat-icon>logout</mat-icon>
              <span>Logout</span>
            </button>
          </mat-menu>
        </mat-toolbar>

        <!-- Page Content -->
        <div class="page-content">
          <ng-content></ng-content>
        </div>
      </mat-sidenav-content>
    </mat-sidenav-container>
  `,
  styles: [`
    .sidenav-container {
      height: 100vh;
    }

    .sidenav {
      width: 280px;
      background: var(--mat-sys-surface-container);
    }

    .toolbar {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      box-shadow: var(--mat-sys-elevation-level2);
    }

    .app-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }

    .app-icon {
      color: var(--mat-sys-on-primary);
    }

    .spacer {
      flex: 1;
    }

    .page-content {
      margin-top: 64px;
      padding: 24px;
      min-height: calc(100vh - 64px);
    }

    .active-link {
      background: var(--mat-sys-primary-container) !important;
      color: var(--mat-sys-on-primary-container) !important;
    }

    .user-info {
      padding: 16px;
      border-bottom: 1px solid var(--mat-sys-outline-variant);
    }

    .user-name {
      font-weight: 500;
      color: var(--mat-sys-on-surface);
    }

    .user-email {
      font-size: 12px;
      color: var(--mat-sys-on-surface-variant);
      margin-top: 4px;
    }

    .theme-controls {
      padding: 16px;
      margin-top: auto;
    }

    .theme-title {
      font-size: 14px;
      font-weight: 500;
      margin: 0 0 12px 0;
      color: var(--mat-sys-on-surface-variant);
    }

    .theme-buttons {
      display: flex;
      gap: 8px;
    }

    .theme-buttons button.active {
      background: var(--mat-sys-primary-container);
      color: var(--mat-sys-on-primary-container);
    }

    /* Mobile adjustments */
    @media (max-width: 768px) {
      .toolbar {
        height: 56px;
      }

      .page-content {
        margin-top: 56px;
        padding: 16px;
        min-height: calc(100vh - 56px);
      }

      .sidenav {
        width: 100%;
        max-width: 320px;
      }
    }
  `]
})
export class NavbarComponent {
  private authService = inject(AuthService);
  private themeService = inject(ThemeService);
  private responsiveService = inject(ResponsiveService);

  // Sidenav state
  private sidenavOpenedSignal = signal(false);

  // Computed signals
  readonly user = this.authService.user;
  readonly isMobile = this.responsiveService.isMobile;
  readonly sidenavMode = computed(() => this.isMobile() ? 'over' : 'side');
  readonly sidenavOpened = computed(() => this.isMobile() ? this.sidenavOpenedSignal() : true);
  readonly themeMode = computed(() => this.themeService.themeConfig().mode);

  readonly navigationItems: NavigationItem[] = [
    { label: 'Dashboard', icon: 'dashboard', route: '/dashboard', tooltip: 'View dashboard and statistics' },
    { label: 'Materials', icon: 'inventory_2', route: '/materials', tooltip: 'Manage materials and inventory' },
    { label: 'Suppliers', icon: 'local_shipping', route: '/suppliers', tooltip: 'Manage suppliers and vendors' },
    { label: 'Customers', icon: 'people', route: '/customers', tooltip: 'Manage customer information' },
    { label: 'Orders', icon: 'shopping_cart', route: '/orders', tooltip: 'View and manage orders' },
    { label: 'GST', icon: 'receipt', route: '/gst', tooltip: 'GST management and billing' },
    { label: 'Reports', icon: 'assessment', route: '/reports', tooltip: 'View reports and analytics' }
  ];

  toggleSidenav(): void {
    this.sidenavOpenedSignal.update(opened => !opened);
  }

  onNavItemClick(): void {
    if (this.isMobile()) {
      this.sidenavOpenedSignal.set(false);
    }
  }

  setThemeMode(mode: 'light' | 'dark' | 'auto'): void {
    this.themeService.setThemeMode(mode);
  }

  logout(): void {
    this.authService.logout().subscribe();
  }
}
