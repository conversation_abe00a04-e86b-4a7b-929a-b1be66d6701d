import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule
  ],
  template: `
    <div class="not-found-container">
      <mat-card class="not-found-card">
        <mat-card-content>
          <div class="not-found-content">
            <mat-icon class="not-found-icon">error_outline</mat-icon>
            <h1 class="not-found-title">404</h1>
            <h2 class="not-found-subtitle">Page Not Found</h2>
            <p class="not-found-description">
              The page you're looking for doesn't exist or has been moved.
            </p>
            <div class="not-found-actions">
              <button mat-raised-button color="primary" routerLink="/dashboard">
                <mat-icon>home</mat-icon>
                Go to Dashboard
              </button>
              <button mat-button (click)="goBack()">
                <mat-icon>arrow_back</mat-icon>
                Go Back
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .not-found-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px;
      background: var(--mat-sys-surface-variant);
    }

    .not-found-card {
      max-width: 500px;
      width: 100%;
      text-align: center;
    }

    .not-found-content {
      padding: 48px 24px;
    }

    .not-found-icon {
      font-size: 72px;
      width: 72px;
      height: 72px;
      color: var(--mat-sys-error);
      margin-bottom: 24px;
    }

    .not-found-title {
      margin: 0 0 16px 0;
      font-size: 64px;
      font-weight: 700;
      color: var(--mat-sys-on-surface);
    }

    .not-found-subtitle {
      margin: 0 0 16px 0;
      font: var(--mat-sys-headline-medium);
      color: var(--mat-sys-on-surface);
    }

    .not-found-description {
      margin: 0 0 32px 0;
      font: var(--mat-sys-body-large);
      color: var(--mat-sys-on-surface-variant);
    }

    .not-found-actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .not-found-actions button {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 480px) {
      .not-found-content {
        padding: 32px 16px;
      }

      .not-found-icon {
        font-size: 56px;
        width: 56px;
        height: 56px;
      }

      .not-found-title {
        font-size: 48px;
      }

      .not-found-actions {
        flex-direction: column;
      }
    }
  `]
})
export class NotFoundComponent {
  goBack(): void {
    window.history.back();
  }
}
