import { Component, OnInit, inject, signal, computed } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBarModule } from '@angular/material/snack-bar';

import { AuthService } from './core/services/auth.service';
import { ThemeService } from './core/services/theme.service';
import { ResponsiveService } from './core/services/responsive.service';
import { NavbarComponent } from './shared/components/navbar/navbar.component';
import { LoadingComponent } from './shared/components/loading/loading.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    MatProgressSpinnerModule,
    MatIconModule,
    MatButtonModule,
    MatSnackBarModule,
    NavbarComponent,
    LoadingComponent
  ],
  template: `
    <!-- Application Loading State -->
    @if (isAppLoading()) {
      <app-loading
        message="Loading Bhavani Doors Admin"
        submessage="Preparing your inventory management system"
        [showIcon]="true"
        icon="store"
        [fullScreen]="true">
      </app-loading>
    } @else {
      <!-- Main Application -->
      <div class="app-container" [class]="containerClasses()">
        <!-- Navigation (only show when authenticated) -->
        @if (isAuthenticated()) {
          <app-navbar></app-navbar>
        }

        <!-- Main Content -->
        <main class="main-content" [class.with-navbar]="isAuthenticated()">
          <router-outlet></router-outlet>
        </main>

        <!-- Offline Indicator -->
        @if (!isOnline()) {
          <div class="offline-banner" role="alert" aria-live="polite">
            <mat-icon>wifi_off</mat-icon>
            <span>You are currently offline. Some features may not be available.</span>
            <button mat-button (click)="checkConnection()" aria-label="Retry connection">
              <mat-icon>refresh</mat-icon>
              Retry
            </button>
          </div>
        }
      </div>
    }
  `,
  styles: [`
    .app-container {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .main-content.with-navbar {
      margin-top: 64px; /* Account for navbar height */
    }

    .offline-banner {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: var(--mat-sys-error-container);
      color: var(--mat-sys-on-error-container);
      padding: 16px;
      display: flex;
      align-items: center;
      gap: 16px;
      z-index: 1000;
      box-shadow: var(--mat-sys-elevation-level3);
    }

    .offline-banner mat-icon {
      color: var(--mat-sys-error);
    }

    .offline-banner button {
      margin-left: auto;
    }

    @media (max-width: 768px) {
      .main-content.with-navbar {
        margin-top: 56px; /* Smaller navbar on mobile */
      }
    }
  `]
})
export class App implements OnInit {
  private authService = inject(AuthService);
  private themeService = inject(ThemeService);
  private responsiveService = inject(ResponsiveService);

  // Application state signals
  protected isAppLoading = signal(true);
  protected isOnline = signal(navigator.onLine);

  // Computed signals
  protected isAuthenticated = computed(() => this.authService.isAuthenticated());
  protected containerClasses = computed(() => this.responsiveService.containerClasses());

  ngOnInit() {
    this.initializeApp();
    this.setupNetworkListeners();
  }

  private async initializeApp() {
    try {
      // Initialize theme
      await this.themeService.initialize();

      // Check authentication status
      await this.authService.checkAuthStatus();

      // Simulate app initialization delay
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      console.error('App initialization error:', error);
    } finally {
      this.isAppLoading.set(false);
    }
  }

  private setupNetworkListeners() {
    window.addEventListener('online', () => this.isOnline.set(true));
    window.addEventListener('offline', () => this.isOnline.set(false));
  }

  protected checkConnection() {
    this.isOnline.set(navigator.onLine);
    if (navigator.onLine) {
      // Optionally trigger a health check
      this.authService.checkAuthStatus();
    }
  }
}
