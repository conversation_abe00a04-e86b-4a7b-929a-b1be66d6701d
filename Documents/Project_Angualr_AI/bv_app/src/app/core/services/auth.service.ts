import { Injectable, signal, computed, inject } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { catchError, tap, map, finalize } from 'rxjs/operators';

import { environment } from '../../../environments/environment';

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_staff: boolean;
  is_superuser: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  user?: User;
  message?: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private http = inject(HttpClient);
  private router = inject(Router);

  // Private signals for internal state management
  private userSignal = signal<User | null>(null);
  private isLoadingSignal = signal<boolean>(false);
  private errorSignal = signal<string | null>(null);

  // Public readonly computed signals
  readonly user = this.userSignal.asReadonly();
  readonly isAuthenticated = computed(() => this.userSignal() !== null);
  readonly isLoading = this.isLoadingSignal.asReadonly();
  readonly error = this.errorSignal.asReadonly();

  // Auth state as observable for compatibility
  readonly authState$ = new BehaviorSubject<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null
  });

  constructor() {
    // Update BehaviorSubject when signals change
    this.updateAuthState();
  }

  /**
   * Login with username and password
   */
  login(credentials: LoginCredentials): Observable<LoginResponse> {
    this.setLoading(true);
    this.clearError();

    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/login/`, credentials, {
      withCredentials: true,
      headers: { 'Content-Type': 'application/json' }
    }).pipe(
      tap(response => {
        if (response.success && response.user) {
          this.setUser(response.user);
          this.router.navigate(['/dashboard']);
        } else {
          this.setError(response.message || 'Login failed');
        }
      }),
      catchError(this.handleError.bind(this)),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * Logout current user
   */
  logout(): Observable<any> {
    this.setLoading(true);

    return this.http.post(`${environment.apiUrl}/auth/logout/`, {}, {
      withCredentials: true
    }).pipe(
      tap(() => {
        this.clearUser();
        this.router.navigate(['/login']);
      }),
      catchError(() => {
        // Even if logout fails on server, clear local state
        this.clearUser();
        this.router.navigate(['/login']);
        return of(null);
      }),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * Check current authentication status
   */
  checkAuthStatus(): Promise<User | null> {
    this.setLoading(true);

    return new Promise((resolve) => {
      this.http.get<User>(`${environment.apiUrl}/auth/user/`, {
        withCredentials: true
      }).pipe(
        tap(user => {
          this.setUser(user);
          resolve(user);
        }),
        catchError(() => {
          this.clearUser();
          resolve(null);
          return of(null);
        }),
        finalize(() => this.setLoading(false))
      ).subscribe();
    });
  }

  /**
   * Refresh authentication status
   */
  refreshAuth(): Observable<User | null> {
    return this.http.get<User>(`${environment.apiUrl}/auth/user/`, {
      withCredentials: true
    }).pipe(
      tap(user => this.setUser(user)),
      catchError(() => {
        this.clearUser();
        return of(null);
      })
    );
  }

  // Private helper methods
  private setUser(user: User): void {
    this.userSignal.set(user);
    this.clearError();
    this.updateAuthState();
  }

  private clearUser(): void {
    this.userSignal.set(null);
    this.clearError();
    this.updateAuthState();
  }

  private setLoading(loading: boolean): void {
    this.isLoadingSignal.set(loading);
    this.updateAuthState();
  }

  private setError(error: string): void {
    this.errorSignal.set(error);
    this.updateAuthState();
  }

  private clearError(): void {
    this.errorSignal.set(null);
    this.updateAuthState();
  }

  private updateAuthState(): void {
    const currentUser = this.userSignal();
    this.authState$.next({
      user: currentUser,
      isAuthenticated: currentUser !== null,
      isLoading: this.isLoadingSignal(),
      error: this.errorSignal()
    });
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unexpected error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = error.error.message;
    } else {
      // Server-side error
      if (error.status === 401) {
        errorMessage = 'Invalid credentials';
      } else if (error.status === 403) {
        errorMessage = 'Access denied';
      } else if (error.status === 0) {
        errorMessage = 'Unable to connect to server';
      } else if (error.error?.message) {
        errorMessage = error.error.message;
      }
    }

    this.setError(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
