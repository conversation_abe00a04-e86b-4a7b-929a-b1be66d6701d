import { Injectable, signal, computed, inject, DOCUMENT } from '@angular/core';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { map } from 'rxjs/operators';

export type ThemeMode = 'light' | 'dark' | 'auto';
export type ThemeColor = 'violet' | 'blue' | 'green' | 'orange' | 'red';

export interface ThemeConfig {
  mode: ThemeMode;
  color: ThemeColor;
  density: number;
  highContrast: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private document = inject(DOCUMENT);
  private breakpointObserver = inject(BreakpointObserver);

  // Theme state signals
  private themeConfigSignal = signal<ThemeConfig>({
    mode: 'auto',
    color: 'violet',
    density: 0,
    highContrast: false
  });

  private systemDarkModeSignal = signal<boolean>(false);

  // Public readonly signals
  readonly themeConfig = this.themeConfigSignal.asReadonly();
  readonly systemDarkMode = this.systemDarkModeSignal.asReadonly();

  // Computed signals
  readonly isDarkMode = computed(() => {
    const config = this.themeConfigSignal();
    if (config.mode === 'auto') {
      return this.systemDarkModeSignal();
    }
    return config.mode === 'dark';
  });

  readonly effectiveTheme = computed(() => ({
    ...this.themeConfigSignal(),
    isDark: this.isDarkMode()
  }));

  // Responsive breakpoints
  readonly isHandset$ = this.breakpointObserver.observe([
    Breakpoints.Handset
  ]).pipe(map(result => result.matches));

  readonly isTablet$ = this.breakpointObserver.observe([
    Breakpoints.Tablet
  ]).pipe(map(result => result.matches));

  readonly isDesktop$ = this.breakpointObserver.observe([
    Breakpoints.Web
  ]).pipe(map(result => result.matches));

  constructor() {
    this.initializeSystemThemeDetection();
  }

  /**
   * Initialize the theme service
   */
  async initialize(): Promise<void> {
    try {
      // Load saved theme preferences
      const savedConfig = this.loadThemeConfig();
      if (savedConfig) {
        this.themeConfigSignal.set(savedConfig);
      }

      // Apply initial theme
      this.applyTheme();

      // Set up theme change listeners
      this.setupThemeListeners();

    } catch (error) {
      console.error('Theme initialization error:', error);
    }
  }

  /**
   * Set theme mode (light, dark, auto)
   */
  setThemeMode(mode: ThemeMode): void {
    const currentConfig = this.themeConfigSignal();
    const newConfig = { ...currentConfig, mode };
    this.themeConfigSignal.set(newConfig);
    this.saveThemeConfig(newConfig);
    this.applyTheme();
  }

  /**
   * Set theme color palette
   */
  setThemeColor(color: ThemeColor): void {
    const currentConfig = this.themeConfigSignal();
    const newConfig = { ...currentConfig, color };
    this.themeConfigSignal.set(newConfig);
    this.saveThemeConfig(newConfig);
    this.applyTheme();
  }

  /**
   * Set theme density
   */
  setThemeDensity(density: number): void {
    const currentConfig = this.themeConfigSignal();
    const newConfig = { ...currentConfig, density: Math.max(-5, Math.min(0, density)) };
    this.themeConfigSignal.set(newConfig);
    this.saveThemeConfig(newConfig);
    this.applyTheme();
  }

  /**
   * Toggle high contrast mode
   */
  toggleHighContrast(): void {
    const currentConfig = this.themeConfigSignal();
    const newConfig = { ...currentConfig, highContrast: !currentConfig.highContrast };
    this.themeConfigSignal.set(newConfig);
    this.saveThemeConfig(newConfig);
    this.applyTheme();
  }

  /**
   * Reset theme to defaults
   */
  resetTheme(): void {
    const defaultConfig: ThemeConfig = {
      mode: 'auto',
      color: 'violet',
      density: 0,
      highContrast: false
    };
    this.themeConfigSignal.set(defaultConfig);
    this.saveThemeConfig(defaultConfig);
    this.applyTheme();
  }

  private initializeSystemThemeDetection(): void {
    // Check initial system preference
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    this.systemDarkModeSignal.set(mediaQuery.matches);

    // Listen for system theme changes
    mediaQuery.addEventListener('change', (e) => {
      this.systemDarkModeSignal.set(e.matches);
      if (this.themeConfigSignal().mode === 'auto') {
        this.applyTheme();
      }
    });
  }

  private setupThemeListeners(): void {
    // Listen for theme config changes and apply them
    // This is handled by the applyTheme() calls in the setter methods
  }

  private applyTheme(): void {
    const config = this.themeConfigSignal();
    const isDark = this.isDarkMode();
    const body = this.document.body;
    const html = this.document.documentElement;

    // Remove existing theme classes
    body.classList.remove('light-theme', 'dark-theme', 'high-contrast');
    html.classList.remove('light-theme', 'dark-theme', 'high-contrast');

    // Apply theme mode
    const themeClass = isDark ? 'dark-theme' : 'light-theme';
    body.classList.add(themeClass);
    html.classList.add(themeClass);

    // Apply high contrast if enabled
    if (config.highContrast) {
      body.classList.add('high-contrast');
      html.classList.add('high-contrast');
    }

    // Set color scheme for CSS
    html.style.colorScheme = isDark ? 'dark' : 'light';

    // Apply theme color as CSS custom property
    html.style.setProperty('--app-theme-color', config.color);
    html.style.setProperty('--app-theme-density', config.density.toString());

    // Apply density class
    body.classList.remove('density-0', 'density--1', 'density--2', 'density--3', 'density--4', 'density--5');
    body.classList.add(`density-${config.density}`);
  }

  private loadThemeConfig(): ThemeConfig | null {
    try {
      const saved = localStorage.getItem('bhavani-doors-theme');
      return saved ? JSON.parse(saved) : null;
    } catch {
      return null;
    }
  }

  private saveThemeConfig(config: ThemeConfig): void {
    try {
      localStorage.setItem('bhavani-doors-theme', JSON.stringify(config));
    } catch (error) {
      console.warn('Failed to save theme config:', error);
    }
  }
}
