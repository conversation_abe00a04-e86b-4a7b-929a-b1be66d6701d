import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  console.log('🛡️ Auth Guard - isAuthenticated:', authService.isAuthenticated());
  console.log('🛡️ Auth Guard - user:', authService.user());
  console.log('🛡️ Auth Guard - route:', state.url);
  console.log('🛡️ Auth Guard - isLoading:', authService.isLoading());

  // If auth is still loading, allow access temporarily to avoid redirect loops
  if (authService.isLoading()) {
    console.log('⏳ Auth Guard - Auth still loading, allowing temporary access');
    return true;
  }

  if (authService.isAuthenticated()) {
    console.log('✅ Auth Guard - Access granted');
    return true;
  } else {
    console.log('❌ Auth Guard - Access denied, redirecting to login');
    router.navigate(['/login'], {
      queryParams: { returnUrl: state.url },
      replaceUrl: true
    });
    return false;
  }
};

// Guard for login page - redirects to dashboard if already authenticated
export const loginGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  console.log('🔐 Login Guard - isAuthenticated:', authService.isAuthenticated());
  console.log('🔐 Login Guard - user:', authService.user());
  console.log('🔐 Login Guard - route:', state.url);
  console.log('🔐 Login Guard - isLoading:', authService.isLoading());

  // If auth is still loading, allow access to login page
  if (authService.isLoading()) {
    console.log('⏳ Login Guard - Auth still loading, allowing access to login');
    return true;
  }

  if (authService.isAuthenticated()) {
    console.log('✅ Login Guard - User authenticated, redirecting to dashboard');
    const returnUrl = route.queryParams['returnUrl'] || '/dashboard';
    router.navigate([returnUrl], { replaceUrl: true });
    return false;
  } else {
    console.log('✅ Login Guard - User not authenticated, allowing access to login');
    return true;
  }
};
