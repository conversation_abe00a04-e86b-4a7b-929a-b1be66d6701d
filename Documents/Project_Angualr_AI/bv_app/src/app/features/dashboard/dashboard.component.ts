import { Component, inject, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';

import { AuthService } from '../../core/services/auth.service';
import { ResponsiveService } from '../../core/services/responsive.service';

interface DashboardCard {
  title: string;
  value: string | number;
  icon: string;
  color: string;
  route: string;
  description: string;
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'neutral';
  };
}

interface QuickAction {
  label: string;
  icon: string;
  route: string;
  color: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatGridListModule,
    MatProgressBarModule,
    MatChipsModule,
    MatDividerModule
  ],
  template: `
    <div class="dashboard-container">
      <!-- Welcome Header -->
      <div class="welcome-header">
        <div class="welcome-content">
          <h1 class="welcome-title">
            Welcome back, {{ user()?.first_name || user()?.username }}!
          </h1>
          <p class="welcome-subtitle">
            Here's what's happening with your inventory today
          </p>
        </div>
        <div class="welcome-actions">
          <button mat-raised-button color="primary" routerLink="/orders/new">
            <mat-icon>add</mat-icon>
            New Order
          </button>
        </div>
      </div>

      <!-- Dashboard Cards Grid -->
      <div class="dashboard-grid" [style.grid-template-columns]="gridColumns()">
        @for (card of dashboardCards; track card.title) {
          <mat-card class="dashboard-card" [routerLink]="card.route">
            <mat-card-content>
              <div class="card-header">
                <div class="card-icon" [style.background-color]="card.color">
                  <mat-icon>{{ card.icon }}</mat-icon>
                </div>
                @if (card.trend) {
                  <div class="card-trend" [class]="'trend-' + card.trend.direction">
                    <mat-icon>
                      {{ card.trend.direction === 'up' ? 'trending_up' : 
                         card.trend.direction === 'down' ? 'trending_down' : 'trending_flat' }}
                    </mat-icon>
                    <span>{{ card.trend.value }}%</span>
                  </div>
                }
              </div>
              
              <div class="card-content">
                <h3 class="card-value">{{ card.value }}</h3>
                <h4 class="card-title">{{ card.title }}</h4>
                <p class="card-description">{{ card.description }}</p>
              </div>
            </mat-card-content>
          </mat-card>
        }
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions-section">
        <h2 class="section-title">Quick Actions</h2>
        <div class="quick-actions-grid">
          @for (action of quickActions; track action.label) {
            <button mat-stroked-button 
                    class="quick-action-button"
                    [routerLink]="action.route">
              <mat-icon [style.color]="action.color">{{ action.icon }}</mat-icon>
              <span>{{ action.label }}</span>
            </button>
          }
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="recent-activity-section">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Recent Activity</mat-card-title>
            <mat-card-subtitle>Latest updates from your system</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="activity-list">
              @for (activity of recentActivities; track activity.id) {
                <div class="activity-item">
                  <div class="activity-icon">
                    <mat-icon [style.color]="activity.color">{{ activity.icon }}</mat-icon>
                  </div>
                  <div class="activity-content">
                    <p class="activity-text">{{ activity.text }}</p>
                    <span class="activity-time">{{ activity.time }}</span>
                  </div>
                </div>
                @if (!$last) {
                  <mat-divider></mat-divider>
                }
              }
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button routerLink="/reports">View All Reports</button>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 24px;
      max-width: 1400px;
      margin: 0 auto;
    }

    .welcome-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;
      padding: 24px;
      background: var(--mat-sys-primary-container);
      color: var(--mat-sys-on-primary-container);
      border-radius: 16px;
    }

    .welcome-title {
      margin: 0 0 8px 0;
      font: var(--mat-sys-headline-large);
      font-weight: 600;
    }

    .welcome-subtitle {
      margin: 0;
      font: var(--mat-sys-body-large);
      opacity: 0.8;
    }

    .dashboard-grid {
      display: grid;
      gap: 24px;
      margin-bottom: 32px;
    }

    .dashboard-card {
      cursor: pointer;
      transition: all 0.2s ease;
      border-radius: 16px;
    }

    .dashboard-card:hover {
      transform: translateY(-2px);
      box-shadow: var(--mat-sys-elevation-level3);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    .card-trend {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 500;
    }

    .trend-up {
      color: var(--mat-sys-success);
    }

    .trend-down {
      color: var(--mat-sys-error);
    }

    .trend-neutral {
      color: var(--mat-sys-on-surface-variant);
    }

    .card-value {
      margin: 0 0 8px 0;
      font: var(--mat-sys-headline-large);
      font-weight: 600;
      color: var(--mat-sys-on-surface);
    }

    .card-title {
      margin: 0 0 8px 0;
      font: var(--mat-sys-title-medium);
      color: var(--mat-sys-on-surface);
    }

    .card-description {
      margin: 0;
      font: var(--mat-sys-body-small);
      color: var(--mat-sys-on-surface-variant);
    }

    .section-title {
      margin: 0 0 16px 0;
      font: var(--mat-sys-headline-medium);
      color: var(--mat-sys-on-surface);
    }

    .quick-actions-section {
      margin-bottom: 32px;
    }

    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .quick-action-button {
      height: 64px;
      display: flex;
      align-items: center;
      gap: 12px;
      justify-content: flex-start;
      padding: 0 20px;
    }

    .recent-activity-section {
      margin-bottom: 32px;
    }

    .activity-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .activity-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 8px 0;
    }

    .activity-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--mat-sys-surface-variant);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .activity-content {
      flex: 1;
    }

    .activity-text {
      margin: 0 0 4px 0;
      font: var(--mat-sys-body-medium);
      color: var(--mat-sys-on-surface);
    }

    .activity-time {
      font: var(--mat-sys-body-small);
      color: var(--mat-sys-on-surface-variant);
    }

    /* Mobile adjustments */
    @media (max-width: 768px) {
      .dashboard-container {
        padding: 16px;
      }

      .welcome-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
      }

      .quick-actions-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class DashboardComponent {
  private authService = inject(AuthService);
  private responsiveService = inject(ResponsiveService);

  // Computed signals
  readonly user = this.authService.user;
  readonly columns = this.responsiveService.getColumnsForScreen;
  readonly gridColumns = computed(() => 
    `repeat(${Math.min(this.columns(), 4)}, 1fr)`
  );

  readonly dashboardCards: DashboardCard[] = [
    {
      title: 'Total Materials',
      value: '1,234',
      icon: 'inventory_2',
      color: '#1976d2',
      route: '/materials',
      description: 'Items in inventory',
      trend: { value: 12, direction: 'up' }
    },
    {
      title: 'Active Orders',
      value: '56',
      icon: 'shopping_cart',
      color: '#388e3c',
      route: '/orders',
      description: 'Pending orders',
      trend: { value: 8, direction: 'up' }
    },
    {
      title: 'Suppliers',
      value: '23',
      icon: 'local_shipping',
      color: '#f57c00',
      route: '/suppliers',
      description: 'Active suppliers',
      trend: { value: 0, direction: 'neutral' }
    },
    {
      title: 'Customers',
      value: '189',
      icon: 'people',
      color: '#7b1fa2',
      route: '/customers',
      description: 'Total customers',
      trend: { value: 15, direction: 'up' }
    }
  ];

  readonly quickActions: QuickAction[] = [
    { label: 'Add Material', icon: 'add_box', route: '/materials/new', color: '#1976d2' },
    { label: 'Create Order', icon: 'add_shopping_cart', route: '/orders/new', color: '#388e3c' },
    { label: 'Add Supplier', icon: 'add_business', route: '/suppliers/new', color: '#f57c00' },
    { label: 'Add Customer', icon: 'person_add', route: '/customers/new', color: '#7b1fa2' },
    { label: 'GST Reports', icon: 'receipt_long', route: '/gst', color: '#d32f2f' },
    { label: 'View Reports', icon: 'analytics', route: '/reports', color: '#1976d2' }
  ];

  readonly recentActivities = [
    {
      id: 1,
      text: 'New order #1234 created by Customer ABC',
      time: '2 hours ago',
      icon: 'shopping_cart',
      color: '#388e3c'
    },
    {
      id: 2,
      text: 'Material "Steel Door Frame" stock updated',
      time: '4 hours ago',
      icon: 'inventory_2',
      color: '#1976d2'
    },
    {
      id: 3,
      text: 'New supplier "XYZ Materials" added',
      time: '1 day ago',
      icon: 'local_shipping',
      color: '#f57c00'
    },
    {
      id: 4,
      text: 'GST report generated for March 2024',
      time: '2 days ago',
      icon: 'receipt_long',
      color: '#d32f2f'
    }
  ];
}
