import { Component, inject, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';

import { AuthService } from '../../../core/services/auth.service';
import { ThemeService } from '../../../core/services/theme.service';
import { ResponsiveService } from '../../../core/services/responsive.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDividerModule,
    MatTooltipModule
  ],
  template: `
    <div class="login-container" [class]="containerClasses()">
      <div class="login-wrapper">
        <!-- App Branding -->
        <div class="app-branding">
          <mat-icon class="app-logo">store</mat-icon>
          <h1 class="app-title">Bhavani Doors</h1>
          <p class="app-subtitle">Inventory & Order Management System</p>
        </div>

        <!-- Login Card -->
        <mat-card class="login-card">
          <mat-card-header>
            <mat-card-title>Welcome Back</mat-card-title>
            <mat-card-subtitle>Sign in to your account</mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
              <!-- Username Field -->
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Username</mat-label>
                <input matInput 
                       formControlName="username" 
                       autocomplete="username"
                       [class.mat-form-field-invalid]="isFieldInvalid('username')">
                <mat-icon matSuffix>person</mat-icon>
                @if (isFieldInvalid('username')) {
                  <mat-error>
                    @if (loginForm.get('username')?.errors?.['required']) {
                      Username is required
                    }
                    @if (loginForm.get('username')?.errors?.['minlength']) {
                      Username must be at least 3 characters
                    }
                  </mat-error>
                }
              </mat-form-field>

              <!-- Password Field -->
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Password</mat-label>
                <input matInput 
                       [type]="hidePassword() ? 'password' : 'text'"
                       formControlName="password"
                       autocomplete="current-password"
                       [class.mat-form-field-invalid]="isFieldInvalid('password')">
                <button mat-icon-button 
                        matSuffix 
                        type="button"
                        (click)="togglePasswordVisibility()"
                        [attr.aria-label]="'Hide password'"
                        [attr.aria-pressed]="!hidePassword()">
                  <mat-icon>{{ hidePassword() ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
                @if (isFieldInvalid('password')) {
                  <mat-error>
                    @if (loginForm.get('password')?.errors?.['required']) {
                      Password is required
                    }
                    @if (loginForm.get('password')?.errors?.['minlength']) {
                      Password must be at least 6 characters
                    }
                  </mat-error>
                }
              </mat-form-field>

              <!-- Error Message -->
              @if (authError()) {
                <div class="error-message" role="alert">
                  <mat-icon>error</mat-icon>
                  <span>{{ authError() }}</span>
                </div>
              }

              <!-- Submit Button -->
              <button mat-raised-button
                      color="primary"
                      type="submit"
                      class="login-button full-width"
                      [disabled]="loginForm.invalid || isLoading()">
                @if (isLoading()) {
                  <mat-spinner diameter="20" strokeWidth="3"></mat-spinner>
                  <span>Signing in...</span>
                } @else {
                  <ng-container>
                    <mat-icon>login</mat-icon>
                    <span>Sign In</span>
                  </ng-container>
                }
              </button>
            </form>
          </mat-card-content>

          <mat-divider></mat-divider>

          <!-- Footer -->
          <mat-card-footer class="login-footer">
            <p class="footer-text">
              Need help? Contact your system administrator
            </p>
          </mat-card-footer>
        </mat-card>

        <!-- Theme Toggle -->
        <div class="theme-toggle">
          <button mat-icon-button 
                  (click)="toggleTheme()"
                  [matTooltip]="themeTooltip()">
            <mat-icon>{{ themeIcon() }}</mat-icon>
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, 
        var(--mat-sys-primary-container) 0%, 
        var(--mat-sys-secondary-container) 100%);
      padding: 24px;
    }

    .login-wrapper {
      width: 100%;
      max-width: 400px;
      position: relative;
    }

    .app-branding {
      text-align: center;
      margin-bottom: 32px;
      color: var(--mat-sys-on-surface);
    }

    .app-logo {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: var(--mat-sys-primary);
      margin-bottom: 16px;
    }

    .app-title {
      margin: 0 0 8px 0;
      font: var(--mat-sys-headline-large);
      font-weight: 600;
    }

    .app-subtitle {
      margin: 0;
      font: var(--mat-sys-body-large);
      color: var(--mat-sys-on-surface-variant);
    }

    .login-card {
      box-shadow: var(--mat-sys-elevation-level3);
      border-radius: 16px;
    }

    .login-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 16px;
    }

    .full-width {
      width: 100%;
    }

    .login-button {
      height: 48px;
      margin-top: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .error-message {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background: var(--mat-sys-error-container);
      color: var(--mat-sys-on-error-container);
      border-radius: 8px;
      font: var(--mat-sys-body-small);
    }

    .error-message mat-icon {
      color: var(--mat-sys-error);
    }

    .login-footer {
      padding: 16px;
      text-align: center;
    }

    .footer-text {
      margin: 0;
      font: var(--mat-sys-body-small);
      color: var(--mat-sys-on-surface-variant);
    }

    .theme-toggle {
      position: absolute;
      top: -60px;
      right: 0;
    }

    /* Mobile adjustments */
    @media (max-width: 480px) {
      .login-container {
        padding: 16px;
      }

      .login-wrapper {
        max-width: 100%;
      }

      .app-logo {
        font-size: 48px;
        width: 48px;
        height: 48px;
      }

      .app-branding {
        margin-bottom: 24px;
      }
    }

    /* Animation */
    .login-card {
      animation: slideUp 0.4s ease-out;
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `]
})
export class LoginComponent {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private themeService = inject(ThemeService);
  private responsiveService = inject(ResponsiveService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private snackBar = inject(MatSnackBar);

  // Form state
  loginForm: FormGroup;
  
  // UI state signals
  private hidePasswordSignal = signal(true);

  // Computed signals
  readonly hidePassword = this.hidePasswordSignal.asReadonly();
  readonly isLoading = this.authService.isLoading;
  readonly authError = this.authService.error;
  readonly containerClasses = this.responsiveService.containerClasses;
  readonly isDarkMode = this.themeService.isDarkMode;

  readonly themeIcon = computed(() => this.isDarkMode() ? 'light_mode' : 'dark_mode');
  readonly themeTooltip = computed(() => 
    `Switch to ${this.isDarkMode() ? 'light' : 'dark'} mode`
  );

  constructor() {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });

    // Pre-fill with test credentials in development
    if (!environment.production) {
      this.loginForm.patchValue({
        username: 'testuser',
        password: 'testpass123'
      });
    }
  }

  onSubmit(): void {
    if (this.loginForm.valid && !this.isLoading()) {
      const credentials = this.loginForm.value;
      
      this.authService.login(credentials).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Login successful!', 'Close', {
              duration: 3000,
              panelClass: ['success-snackbar']
            });
            
            // Navigate to return URL or dashboard
            const returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
            this.router.navigate([returnUrl]);
          }
        },
        error: (error) => {
          this.snackBar.open(error.message || 'Login failed', 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  togglePasswordVisibility(): void {
    this.hidePasswordSignal.update(hidden => !hidden);
  }

  toggleTheme(): void {
    const currentMode = this.themeService.themeConfig().mode;
    const newMode = currentMode === 'dark' ? 'light' : 'dark';
    this.themeService.setThemeMode(newMode);
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }
}
